import os, boto3, json, uuid
from datetime import datetime, timedelta, timezone
from pymongo import MongoClient
from langchain.tools import StructuredTool
from requests_aws4auth import AWS4Auth
from opensearchpy import OpenSearch, RequestsHttpConnection
from services.targets_service import set_target as set_target_api_call
from services.machine_learning import create_plans_and_routines as create_plans_and_routines_api_call
from typing import Optional
from langchain.embeddings import OpenAIEmbeddings

# AWS Configuration
AWS_REGION = "us-east-1"
AWS_SERVICE = "es"  # OpenSearch

# Get AWS credentials from environment variables or IAM role
session = boto3.Session()
credentials = session.get_credentials()
aws_auth = AWS4Auth(credentials.access_key, credentials.secret_key, 
                     AWS_REGION, AWS_SERVICE, session_token=credentials.token)

# OpenSearch Configuration
OPENSEARCH_HOST =  os.environ.get("OPENSEARCH_HOST")
MONGO_URI =  os.environ.get("MONGO_URI")

# Initialize OpenAI
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")

# Static JSON Lambda
TRACKERS_STATIC_DATA_LAMBDA = os.environ.get("TRACKERS_STATIC_DATA_LAMBDA")

if not OPENSEARCH_HOST or not MONGO_URI or not TRACKERS_STATIC_DATA_LAMBDA or not OPENAI_API_KEY:
    print("Required Env variables not found")
    exit(1)

embedding_model = OpenAIEmbeddings(model="text-embedding-3-small", openai_api_key=OPENAI_API_KEY)

# OpenSearch Client with AWS IAM Authentication
os_client = OpenSearch(
    hosts=[{"host": OPENSEARCH_HOST, "port": 443}],
    http_auth=aws_auth,
    use_ssl=True,
    verify_certs=True,
    connection_class=RequestsHttpConnection
)

# Setup Mongo Client
client = MongoClient(MONGO_URI)
db = client["MealLog"]

# Use session credentials to create a Lambda client
lambda_client = boto3.client(
    "lambda",
    region_name=AWS_REGION,
    aws_access_key_id=credentials.access_key,
    aws_secret_access_key=credentials.secret_key,
    aws_session_token=credentials.token
)

# -------------------------------------------------------------------------
# Chat Session Tools
# -------------------------------------------------------------------------

def fetch_last_messages(user_id: str, size: int):
    search_body = {
        "size": size,
        "_source": { "excludes": ["userId", "vector"] },
        "query": {"match": {"userId": user_id}},
        "sort": [{"timestamp": "desc"}]
    }
    response = os_client.search(index="chat-memory", body=search_body)

    messages = []
    for hit in response["hits"]["hits"]:
        source = hit["_source"]
        messages += source['messages']
    return messages

def upsert_session_message(user_id: str, new_messages: list):
    now = datetime.utcnow()
    now_iso = now.isoformat()
    
    msg_id = str(uuid.uuid4())
    session_text = "\n".join(f"{m['role']}: {m['content']}" for m in new_messages)
    vector = embedding_model.embed_query(session_text)    
    doc = {
        "userId": user_id,
        "timestamp": now_iso,
        "messages": new_messages,
        "vector": vector
    }
    os_client.index(index="chat-memory", id=msg_id, body=doc)

def retrieve_relevant_messages(user_id: str, user_message: str, k=3):
    query_vector = embedding_model.embed_query(user_message)

    search_body = {
        "size": k,
        "query": {
            "bool": {
                "must": [
                    {
                        "knn": { "vector": { "vector": query_vector, "k": k } }
                    },
                    {
                        "match": { "userId": user_id }
                    }
                ]
            }
        }
    }

    return os_client.search(index="chat-memory", body=search_body)

def retrieve_chat_history(user_id: str, user_message: str, page: int = 1, limit: int = 10):
    from_index = (page - 1) * limit 

    if user_message:
        query_vector = embedding_model.embed_query(user_message)
        search_body = {
            "from": from_index, "size": limit,
            "_source": { "excludes": ["userId", "vector"] },
            "query": {
                "bool": {
                    "must": [
                        {
                            "knn": { "vector": { "vector": query_vector, "k": from_index + limit + 2 } }
                        },
                        {
                            "match": { "userId": user_id }
                        }
                    ]
                }
            },
            "sort": [{"timestamp": "desc"}]
        }

    else:
        search_body = {
            "from": from_index, "size": limit,
            "_source": { "excludes": ["userId", "vector"] },
            "query": {"match": {"userId": user_id}},
            "sort": [{"timestamp": "desc"}]
        }

    response = os_client.search(index="chat-memory", body=search_body)

    # Restructure messages
    results = []
    for hit in response["hits"]["hits"]:
        for msg in hit["_source"]["messages"]:
            user_guid = 'agent'
            feedback_msg = None
            if 'feedback' in hit["_source"]:
                feedback_msg = hit["_source"]['feedback']
            if msg['role'] == 'user':
                user_guid = user_id
                feedback_msg = None
                
            new_message = {
                'id': hit['_id'],
                'message': msg['content'],
                'created_at': hit['_source']['timestamp'],
                'user': {'user_guid': user_guid},
                'feedback': feedback_msg,
                'file_type': None
            }
            results.append(new_message)    

    return results

def update_message_feedback(message_id: str, feedback: str):
    if not os_client.exists(index="chat-memory", id=message_id):
        return False

    os_client.update(
        index="chat-memory",
        id=message_id,
        body={
            "doc": { "feedback": feedback }
        }
    )
    return True    

# -------------------------------------------------------------------------
# Writer Tool -> stores results in user_recommendations index
# -------------------------------------------------------------------------
def store_user_recommendations(data):
    """Stores data into user_recommendations."""
    print("Storing User Recommendations")
    # print(data)
    index = "user_recommendations"
    response = os_client.index(index=index, body=data)
    result = response.get('result')
    if result in ['created', 'updated']:
        print("Data successfully saved.")
        return True
    else:
        print(f"Failed to save data. Response: {response}")
        return False

# -------------------------------------------------------------------------
# Tool to fetch User Profile with Biometrics
# -------------------------------------------------------------------------
def fetch_user_profile(user_id: str):
    """
    Fetches the user's profile including demographic info and personal health goals.

    Goal: Used to personalize guidance and determine eligibility for suggestions.
    """
    print("Fetching User Profile")
    print(f"User: {user_id}")
    index = "user_profiles"
    query = {"query": {"match": {"userId.keyword": user_id}}}
    
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_user_profile_tool = StructuredTool.from_function(fetch_user_profile)

# -------------------------------------------------------------------------
# Tool to Fetch Daily Activity Logs
# -------------------------------------------------------------------------
def fetch_daily_activity(user_id: str, start_date: str, end_date: str):
    """Fetches user daily activity summary logs (steps, elevation, floors, distance, calories, activity time, etc.)."""
    print("Fetching Daily Activity Summary")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "activity_summary"
    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"timestamp": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_activity_tool = StructuredTool.from_function(fetch_daily_activity)

# -------------------------------------------------------------------------
# Tool to Fetch Sleep Logs
# -------------------------------------------------------------------------
def fetch_daily_sleep(user_id: str, start_date: str, end_date: str):
    """Fetches user sleep logs data."""
    print("Fetching Sleep Logs")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "sleep"
    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"timestamp": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    
    response = os_client.search(index=index, body=query)
    data = [hit["_source"]["summary"] if "summary" in hit["_source"] else hit["_source"] for hit in response["hits"]["hits"]]
    return data

fetch_sleep_tool = StructuredTool.from_function(fetch_daily_sleep)

# -------------------------------------------------------------------------
#### Tool to Fetch Daily Target Achievements
# -------------------------------------------------------------------------

# TODO: Target id map
def fetch_target_achievements(user_id: str, start_date: str, end_date: str):
    """Fetches user target achievements with date, value and percentage target achieved for each health tracker."""
    print("Fetching Target Achievements")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "user_targets_achievements"

    target_map = {
        1: "Water",
        2: "WHR",
        3: "Steps",
        4: "Sleep",
        5: "Active Minutes",
        6: "Active Energy(Calories burned)",
        7: "Resting Energy(Calories burned)",
        8: "Stand minutes",
        9: "Standing hours",
        10: "Exercise minutes",
        11: "Mindfulness",
        12: "Deep Sleep hours",
        13: "Flights climbed",
        14: "Activity distance",
        15: "Nutrition",
        16: "Vo2 Max",
        17: "Resting Heart Rate",
        18: "Weight",
        19: "Blood Pressure",
        20: "Blood Glucose",
        21: "SpO2",
        22: "Heart Rate Variability",
        23: "BMI",
        24: "Body Fat",
        25: "Quad size",
        26: "Hip size",
        27: "Chest size",
        28: "Arm size",
        29: "Exercise Score",
        30: "Carbs",
        31: "Fats",
        32: "Protein",
        33: "Exercise Count",
        34: "Mindfulness Score",
        35: "Mindfulness Count",
        36: "Fiber",
        37: "Total burned calories",
        38: "Intermittent Fasting",
        39: "Waist Size",
        40: "Continuous Glucose Monitoring",
        41: "Heart Rate",
        42: "ECG",
        43: "Height",
        44: "Body Temperature"
    }

    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"date": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]

    updated_data = []
    for d in data:
        target_id = d.get("targetId")
        if target_id and target_map.get(target_id):
            d["targetName"] = target_map.get(target_id)
            if "logIds" in d: del d["logIds"]
            updated_data.append(d)

    return updated_data

fetch_target_tool = StructuredTool.from_function(fetch_target_achievements)

# -------------------------------------------------------------------------
#### Tool to Fetch Meal Logs
# -------------------------------------------------------------------------
def fetch_meal_logs(user_id: str, start_date: str, end_date: str):
    """Fetches user Meal Logs."""
    print("Fetching Meal Logs")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")

    collection = db["Meal_Logs"]
    start_dt = datetime.fromisoformat(start_date)
    end_dt = datetime.fromisoformat(end_date) + timedelta(days=1)
    query = {
        "userId.keyword": user_id,
        "date": {"$gte": start_dt, "$lte": end_dt}
    }
    projection = {
        '_id': 0,
        'type': 1, 'foodType': 1, 'fruitVegPercentage': 1,
        'nutriScore': 1, 'grade': 1, 'mealTags': 1
    }
    cursor = collection.find(query, projection).limit(100)
    data = list(cursor)

    fields_to_remove = ['tagId', 'subImgId', 'imagePath', 'coordinates']
    for doc in data:
        if "mealTags" in doc:
            for mt in doc["mealTags"]:
                for f in fields_to_remove:
                    if f in mt: del mt[f]

    return data

fetch_meal_logs_tool = StructuredTool.from_function(fetch_meal_logs)

# -------------------------------------------------------------------------
#### Tool to Fetch Meal Plans Assigned To that User
# -------------------------------------------------------------------------
def fetch_assigned_meal_plans(user_id: str):
    """Fetches meal plans assigned to a user"""
    print("Fetching meal plans assigned to user")
    print(f"User: {user_id}")
    
    # Step 1: Get assigned meal plan IDs
    assignments = db["User_Meal_Plans"].find({ "assignedTo": user_id })
    meal_plan_ids = [assignment["mealPlanId"] for assignment in assignments if "mealPlanId" in assignment]

    if not meal_plan_ids:
        meal_plan_ids = []

    # Step 2: Fetch full meal plan documents from 'meal_plans' collection
    meal_plans = list(db["Meal_Plans"].find({
        "$or": [
            { "createdBy": user_id },
            { "isVisible": True },
            { "_id": { "$in": meal_plan_ids } }
        ]
    }))

    return meal_plans

fetch_assigned_meal_plans_tool = StructuredTool.from_function(fetch_assigned_meal_plans)

# -------------------------------------------------------------------------
#### Tool to Fetch Exercise Logs
# -------------------------------------------------------------------------
def fetch_exercise_logs(user_id: str, start_date: str, end_date: str):
    """Fetches user exercise logs"""
    print("Fetching Exercise Logs")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "exercise_logs"
    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"timestamp": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_exercise_tool = StructuredTool.from_function(fetch_exercise_logs)

# -------------------------------------------------------------------------
#### Tool to Fetch Exercise Templates
# -------------------------------------------------------------------------
allExerciseTemplates = []
def fetch_exercise_templates():
    """Fetches all exercise templates"""
    global allExerciseTemplates
    if allExerciseTemplates:
        return allExerciseTemplates

    print("Fetching All Exercise Templates")
    index = "exercise_templates"
    query = {
        "size": 100,
        "query": {
            "match_all": {}
        }
    }
    
    response = os_client.search(index=index, body=query)
    allExerciseTemplates = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return allExerciseTemplates

fetch_exercise_templates_tool = StructuredTool.from_function(fetch_exercise_templates)

# -------------------------------------------------------------------------
#### Tool to Fetch Exercise Routines Assigned To that User
# -------------------------------------------------------------------------
def fetch_assigned_exercise_routines(user_id: str):
    """Fetches exercise routines assigned to a user"""
    print("Fetching exercise routines assigned to user")
    print(f"User: {user_id}")
    
    # Step 1: Get assigned routine IDs from 'user_exercise_routines' index
    assigned_index = "user_exercise_routines"
    assignment_query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [{ "match": { "assignedTo.keyword": user_id } }],
            }
        }
    }
    assignment_response = os_client.search(index=assigned_index, body=assignment_query)
    routine_ids = [hit["_source"]["routineId"] for hit in assignment_response["hits"]["hits"] if "_source" in hit]

    if not routine_ids:
        routine_ids = []

    # Step 2: Fetch full routine documents from 'exercise_routines' index
    routine_index = "exercise_routines"
    routine_query = {
        "size": 100,
        "query": {
            "bool": {
                "should": [
                    { "match": { "createdBy.keyword": user_id } },
                    { "match": { "isVisible": True } },
                    { "terms": { "_id": routine_ids } }
                ]
            }
        }
    }
    routine_response = os_client.search(index=routine_index, body=routine_query)
    routines = [hit["_source"] for hit in routine_response["hits"]["hits"] if "_source" in hit]

    return routines

fetch_assigned_exercise_routines_tool = StructuredTool.from_function(fetch_assigned_exercise_routines)

# -------------------------------------------------------------------------
#### Tool to Fetch Mindfulness Logs
# -------------------------------------------------------------------------
def fetch_mindfulness_logs(user_id: str, start_date: str, end_date: str):
    """Fetches user mindfulness logs"""
    print("Fetching Mindfulness Logs")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "mindfulness_logs"
    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"timestamp": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_mindfulness_tool = StructuredTool.from_function(fetch_mindfulness_logs)

# -------------------------------------------------------------------------
#### Tool to Fetch Mindfulness Templates
# -------------------------------------------------------------------------
allMindfulnessTemplates = []
def fetch_mindfulness_templates():
    """Fetches all mindfulness templates"""
    global allMindfulnessTemplates
    if allMindfulnessTemplates:
        return allMindfulnessTemplates
    print("Fetching All Mindfulness Templates")
    index = "mindfulness_templates"
    query = {
        "size": 100,
        "query": {
            "match_all": {}
        }
    }
    
    response = os_client.search(index=index, body=query)
    allMindfulnessTemplates = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return allMindfulnessTemplates

fetch_mindfulness_templates_tool = StructuredTool.from_function(fetch_mindfulness_templates)

# -------------------------------------------------------------------------
#### Tool to Fetch Mindfulness Routines Assigned To that User
# -------------------------------------------------------------------------
def fetch_assigned_mindfulness_routines(user_id: str):
    """Fetches mindfulness routines assigned to a user"""
    print("Fetching mindfulness routines assigned to user")
    print(f"User: {user_id}")
    
    # Step 1: Get assigned routine IDs from 'user_mindfulness_routines' index
    assigned_index = "user_mindfulness_routines"
    assignment_query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [{ "match": { "assignedTo.keyword": user_id } }],
            }
        }
    }
    assignment_response = os_client.search(index=assigned_index, body=assignment_query)
    routine_ids = [hit["_source"]["routineId"] for hit in assignment_response["hits"]["hits"] if "_source" in hit]

    if not routine_ids:
        routine_ids = []

    # Step 2: Fetch full routine documents from 'mindfulness_routines' index
    routine_index = "mindfulness_routines"
    routine_query = {
        "size": 100,
        "query": {
            "bool": {
                "should": [
                    { "match": { "createdBy.keyword": user_id } },
                    { "match": { "isVisible": True } },
                    { "terms": { "_id": routine_ids } }
                ]
            }
        }
    }
    routine_response = os_client.search(index=routine_index, body=routine_query)
    routines = [hit["_source"] for hit in routine_response["hits"]["hits"] if "_source" in hit]

    return routines

fetch_assigned_mindfulness_routines_tool = StructuredTool.from_function(fetch_assigned_mindfulness_routines)

# -------------------------------------------------------------------------
#### Tool to Fetch Water Logs
# -------------------------------------------------------------------------
def fetch_water_logs(user_id: str, start_date: str, end_date: str):
    """Fetches user water intake logs."""
    print("Fetching Water Logs")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "water"
    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"timestamp": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_water_logs_tool = StructuredTool.from_function(fetch_water_logs)

# -------------------------------------------------------------------------
#### Tool to BP Logs
# -------------------------------------------------------------------------
def fetch_bp_logs(user_id: str, start_date: str, end_date: str):
    """Fetches user Blood Pressure logs"""
    print("Fetching BP Logs")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "bp"
    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"timestamp": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_bp_tool = StructuredTool.from_function(fetch_bp_logs)

# -------------------------------------------------------------------------
#### Tool to BG Logs
# -------------------------------------------------------------------------
def fetch_bg_logs(user_id: str, start_date: str, end_date: str):
    """Fetches user Blood Glucose logs"""
    print("Fetching BG Logs")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "bg"
    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"timestamp": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_bg_tool = StructuredTool.from_function(fetch_bg_logs)

# -------------------------------------------------------------------------
#### Tool to Activity Logs
# -------------------------------------------------------------------------
def fetch_activity_logs(user_id: str, start_date: str, end_date: str):
    """Fetches user Activity logs"""
    print("Fetching Activity Logs")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "activity"
    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"timestamp": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_activity_tool = StructuredTool.from_function(fetch_activity_logs)

# -------------------------------------------------------------------------
#### Tool to EGVS Logs
# -------------------------------------------------------------------------
def fetch_egvs_logs(user_id: str, start_date: str, end_date: str):
    """Fetches user EGVS logs"""
    print("Fetching EGVS Logs")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "egvs"
    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"timestamp": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_egvs_tool = StructuredTool.from_function(fetch_egvs_logs)

# -------------------------------------------------------------------------
#### Tool to SPO2 Logs
# -------------------------------------------------------------------------
def fetch_spo2_logs(user_id: str, start_date: str, end_date: str):
    """Fetches user SPO2 logs"""
    print("Fetching SPO2 Logs")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "spo2"
    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"timestamp": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_spo2_tool = StructuredTool.from_function(fetch_spo2_logs)

# -------------------------------------------------------------------------
#### Tool to Heart Rate Logs
# -------------------------------------------------------------------------
def fetch_heart_rate_logs(user_id: str, start_date: str, end_date: str):
    """Fetches user Heart Rate logs"""
    print("Fetching Heart Rate Logs")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "heart_rate"
    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"timestamp": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_heart_rate_tool = StructuredTool.from_function(fetch_heart_rate_logs)

# -------------------------------------------------------------------------
#### Tool to Resting Heart Rate Logs
# -------------------------------------------------------------------------
def fetch_resting_heart_rate_logs(user_id: str, start_date: str, end_date: str):
    """Fetches user Resting Heart Rate logs"""
    print("Fetching Resting Heart Rate Logs")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "resting_heart_rate"
    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"timestamp": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_resting_heart_rate_tool = StructuredTool.from_function(fetch_resting_heart_rate_logs)

# -------------------------------------------------------------------------
#### Tool to HRV Logs
# -------------------------------------------------------------------------
def fetch_hrv_logs(user_id: str, start_date: str, end_date: str):
    """Fetches user Resting Heart Rate logs"""
    print("Fetching HRV Logs")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "hrv"
    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"timestamp": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_hrv_tool = StructuredTool.from_function(fetch_hrv_logs)

# -------------------------------------------------------------------------
#### Tool to VO2 Logs
# -------------------------------------------------------------------------
def fetch_vo2_logs(user_id: str, start_date: str, end_date: str):
    """Fetches user VO2 logs"""
    print("Fetching VO2 Logs")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "vo2"
    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"timestamp": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_vo2_tool = StructuredTool.from_function(fetch_vo2_logs)

# -------------------------------------------------------------------------
#### Tool to ECG Logs
# -------------------------------------------------------------------------
def fetch_ecg_logs(user_id: str, start_date: str, end_date: str):
    """Fetches user ECG logs"""
    print("Fetching ECG Logs")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "ecg"
    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"timestamp": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_ecg_tool = StructuredTool.from_function(fetch_ecg_logs)

# -------------------------------------------------------------------------
#### Tool to Height Logs
# -------------------------------------------------------------------------
def fetch_height_logs(user_id: str, start_date: str, end_date: str):
    """Fetches user Height logs"""
    print("Fetching Height Logs")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "height"
    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"timestamp": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_height_tool = StructuredTool.from_function(fetch_height_logs)

# -------------------------------------------------------------------------
#### Tool to Weight Logs
# -------------------------------------------------------------------------
def fetch_weight_logs(user_id: str, start_date: str, end_date: str):
    """Fetches user Weight logs"""
    print("Fetching Weight Logs")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "weight"
    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"timestamp": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_weight_tool = StructuredTool.from_function(fetch_weight_logs)

# -------------------------------------------------------------------------
#### Tool to Fat Logs
# -------------------------------------------------------------------------
def fetch_fat_logs(user_id: str, start_date: str, end_date: str):
    """Fetches user fat % logs"""
    print("Fetching Fat Logs")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "fat"
    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"timestamp": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_fat_tool = StructuredTool.from_function(fetch_fat_logs)

# -------------------------------------------------------------------------
#### Tool to BMI Logs
# -------------------------------------------------------------------------
def fetch_bmi_logs(user_id: str, start_date: str, end_date: str):
    """Fetches user BMI logs"""
    print("Fetching BMI Logs")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "bmi"
    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"timestamp": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_bmi_tool = StructuredTool.from_function(fetch_bmi_logs)

# -------------------------------------------------------------------------
#### Tool to Temperature Logs
# -------------------------------------------------------------------------
def fetch_temp_logs(user_id: str, start_date: str, end_date: str):
    """Fetches user Body Temperature logs"""
    print("Fetching Temperature Logs")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "temp"
    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"timestamp": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_temp_tool = StructuredTool.from_function(fetch_temp_logs)

# -------------------------------------------------------------------------
#### Tool to Waist Size Logs
# -------------------------------------------------------------------------
def fetch_waist_size_logs(user_id: str, start_date: str, end_date: str):
    """Fetches user Waist Size logs"""
    print("Fetching Waist Size Logs")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "waist_size"
    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"timestamp": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_waist_size_tool = StructuredTool.from_function(fetch_waist_size_logs)

# -------------------------------------------------------------------------
#### Tool to Hip Size Logs
# -------------------------------------------------------------------------
def fetch_hip_size_logs(user_id: str, start_date: str, end_date: str):
    """Fetches user Hip Size logs"""
    print("Fetching Hip Size Logs")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "hip_size"
    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"timestamp": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_hip_size_tool = StructuredTool.from_function(fetch_hip_size_logs)

# -------------------------------------------------------------------------
#### Tool to Chest Size Logs
# -------------------------------------------------------------------------
def fetch_chest_size_logs(user_id: str, start_date: str, end_date: str):
    """Fetches user Chest Size logs"""
    print("Fetching Chest Size Logs")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "chest_size"
    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"timestamp": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_chest_size_tool = StructuredTool.from_function(fetch_chest_size_logs)

# -------------------------------------------------------------------------
#### Tool to Arm Size Logs
# -------------------------------------------------------------------------
def fetch_arm_size_logs(user_id: str, start_date: str, end_date: str):
    """Fetches user Arm Size logs"""
    print("Fetching Arm Size Logs")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "arm_size"
    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"timestamp": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_arm_size_tool = StructuredTool.from_function(fetch_arm_size_logs)

# -------------------------------------------------------------------------
#### Tool to Quad Size Logs
# -------------------------------------------------------------------------
def fetch_quad_size_logs(user_id: str, start_date: str, end_date: str):
    """Fetches user Quad Size logs"""
    print("Fetching Quad Size Logs")
    print(f"User: {user_id}, Start Date: {start_date}, End Date: {end_date}")
    index = "quad_size"
    query = {
        "size": 100,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                    {"range": {"timestamp": {"gte": start_date, "lte": end_date}}}
                ]
            }
        }
    }
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_quad_size_tool = StructuredTool.from_function(fetch_quad_size_logs)

# -------------------------------------------------------------------------
#### Tool to User Recommendations
# -------------------------------------------------------------------------
def fetch_user_recommendations(user_id: str):
    """Fetches User Recommendations"""
    print("Fetching User Recommendations")
    print(f"User: {user_id}")
    current_time = datetime.now()
    index = "user_recommendations"
    query = {
        "size": 100,
        "query": {
            "bool": {
            "must": [
                { "match": { "userId.keyword": user_id } },
                { "match": { "status": "open" } },
                { "range": { "ttl": { "gte": current_time } } },
            ]
            },
        },
    }
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_user_recommendations_tool = StructuredTool.from_function(fetch_user_recommendations)

# -------------------------------------------------------------------------
#### Tool to Devices details
# -------------------------------------------------------------------------
def fetch_devices_details(user_id: str):
    """Fetches device details connected to the user"""
    print("Fetching Devices Details")
    print(f"User: {user_id}")
    index = "devices"
    query = {
        "size": 1,
        "query": {
            "bool": {
                "must": [
                    {"match": {"userId.keyword": user_id}},
                ]
            }
        }
    }
    response = os_client.search(index=index, body=query)
    data = [hit["_source"] for hit in response["hits"]["hits"] if "_source" in hit]
    return data

fetch_devices_tool = StructuredTool.from_function(fetch_devices_details)

# -------------------------------------------------------------------------
#### Tool to Fetch Trackers Static JSONs
# -------------------------------------------------------------------------
static_data = {}
def fetch_static_jsons():
    """
    Fetches static JSONs from a Lambda function
    This will return four static JSONs:
    1. targetsMap: All the targets supported by platform along with parameters like duration, unit & respective trackerId, etc
    2. trackerMap: All the trackers supported by platform e.g. Water, Sleep, etc
    3. devices: All the devices supported by platform along with trackerIds they support & respective sourceId
    4. sources: All the data sources supported by platform e.g. Fitbit, Apple Health, etc
    """
    global static_data
    if static_data:
        return static_data
    try:
        response = lambda_client.invoke(
            FunctionName=TRACKERS_STATIC_DATA_LAMBDA,
            InvocationType="RequestResponse",  
            Payload=b"{}"
        )

        # Read and decode the payload from the original response
        response_payload = response['Payload'].read()
        payload_dict = json.loads(response_payload)

        if payload_dict.get("statusCode") == 200:
            body = json.loads(payload_dict.get("body", "{}")).get("data", {})
            static_data = {k: body.get(k) for k in ("targetsMap", "trackerMap", "devices", "sources")}
            return static_data

        print(f"Lambda returned non-200 statusCode: {payload_dict.get('statusCode')}")
        return {}

    except Exception as e:
        print(f"Error invoking Lambda: {e}")
        return {}

fetch_static_jsons_tool = StructuredTool.from_function(fetch_static_jsons)

# -------------------------------------------------------------------------
#### Tool to Set User Target
# -------------------------------------------------------------------------
def set_target(user_id: str, target_id: int, value: float, is_active: bool, access_token: str):
    """
    Sets a target for a user by calling the targets API.
    Here, target_id belongs to the targetMap returned by the static data lambda tool and is determined by the agent based on the target name.

    Args:
        user_id: User ID
        target_id: ID of the target to set (agent should determine this from target name)
        value: Target value to set
        is_active: True/False based on whether user wants to set/remove target
        access_token: User's access token (optional, will be provided by the agent framework)
    
    Returns:
        API response or error message
    """
    print(f"Setting target for user: {user_id}, Target ID: {target_id}, Value: {value}, is_active: {is_active}")
    
    # Call the service to set the target
    result = set_target_api_call(access_token, target_id, value, is_active)
    return result

set_target_tool = StructuredTool.from_function(set_target)

# -------------------------------------------------------------------------
 #### Tool to Update User Profile
 # -------------------------------------------------------------------------
def update_user_profile(user_id: str, goal: Optional[str] = None, gender: Optional[str] = None, age: Optional[int] = None, height_value: Optional[float] = None, weight_value: Optional[float] = None):
    """
    Updates specific fields of an existing user profile in the user_profiles index.
    Only updates goal, weight, height, gender, and age.
    Will not create a new profile if it doesn't exist.
    Height is always stored in centimeters (cm) and weight in kilograms (Kg).
    The AI agent will handle any necessary unit conversions before calling this function.
    Args:
        user_id: User ID (required)
        goal: User's health goal (e.g., "Losing Weight")
        gender: User's gender (e.g., "Male", "Female", "Non-binary")
        age: User's age
        height_value: Height value in centimeters (cm)
        weight_value: Weight value in kilograms (Kg)
    Returns:
        Document ID or error message
    """
    print(f"Updating user profile for user: {user_id}")

    # Search for existing profile
    index = "user_profiles"
    query = {"query": {"match": {"userId.keyword": user_id}}}

    response = os_client.search(index=index, body=query)
    hits = response["hits"]["hits"]

    # If profile doesn't exist, return error
    if not hits:
        print(f"Profile for user {user_id} doesn't exist")
        return {"error": "Profile doesn't exist", "message": "Cannot update a non-existent profile"}

    existing_doc = hits[0]["_source"]
    doc_id = hits[0]["_id"]

    # Prepare the document
    now = datetime.now(timezone.utc).isoformat(timespec='milliseconds').replace('+00:00', 'Z')
    print(f"Current time: {now}")
    # Start with existing document
    doc = existing_doc

    # Update goal
    if goal is not None:
        doc["goal"] = goal

    # Update demographics
    if gender is not None or age is not None:
        if "demographics" not in doc:
            doc["demographics"] = {}
        if gender is not None:
            doc["demographics"]["gender"] = gender
        if age is not None:
            doc["demographics"]["age"] = age

    # Update biometrics
    if height_value is not None or weight_value is not None:
        if "baseline" not in doc:
            doc["baseline"] = {}
        if "biometrics" not in doc["baseline"]:
            doc["baseline"]["biometrics"] = {}

        # Update height
        if height_value is not None:
            if "height" not in doc["baseline"]["biometrics"]:
                doc["baseline"]["biometrics"]["height"] = {}
            doc["baseline"]["biometrics"]["height"]["value"] = height_value
            # Always use cm for height
            doc["baseline"]["biometrics"]["height"]["unit"] = "cm"

        # Update weight
        if weight_value is not None:
            if "weight" not in doc["baseline"]["biometrics"]:
                doc["baseline"]["biometrics"]["weight"] = {}
            doc["baseline"]["biometrics"]["weight"]["value"] = weight_value
            # Always use Kg for weight
            doc["baseline"]["biometrics"]["weight"]["unit"] = "Kg"

    # Update lastUpdated timestamp
    doc["baseline"]["lastUpdated"] = now

    try:
        # Update existing document
        response = os_client.update(
            index=index,
            id=doc_id,
            body={"doc": doc},
            refresh=True
        )
        print(f"Updated user profile with ID: {doc_id}")
        return {"id": doc_id, "result": response.get("result", "updated")}
    except Exception as e:
        error_msg = f"Error updating user profile: {str(e)}"
        print(error_msg)
        return {"error": error_msg}

update_user_profile_tool = StructuredTool.from_function(update_user_profile)

 # -------------------------------------------------------------------------
 #### Tool to Create or Update Plans and Routines
 # -------------------------------------------------------------------------
def create_plans_and_routines(user_id: str, access_token: str, message: str):
    """
    Creates or updates plans and routines for a user by calling the ML service API.
    IMPORTANT: Only call this tool when there is potential to create or update exercise routines,
    mindfulness routines, or meal plans for the user. The ML service will automatically determine
    what types of plans and routines to create based on the user's profile and goals.
    Args:
        user_id: User ID (required)
        access_token: User's access token (optional, will be provided by the agent framework)
        user_data: User's health goal or any update like dietary preference, current weight/height
                Example: {"goal": "Losing Weight"}
    Returns:
        API response or error message
    """
    print(f"Creating/updating plans and routines for user: {user_id}")



    # Call the ML service
    result = create_plans_and_routines_api_call(access_token, message)
    return result

create_plans_and_routines_tool = StructuredTool.from_function(create_plans_and_routines)
